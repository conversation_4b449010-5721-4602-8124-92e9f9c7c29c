'use client'

import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'
import { UserIcon } from '@heroicons/react/24/outline'

function Navbar() {
  const { data: session } = useSession()
  const pathname = usePathname()
  const [userAvatar, setUserAvatar] = useState<string | null>(null)

  // 注意：会话持久性现在由全局SessionPersistenceWrapper处理

  // 获取用户头像
  useEffect(() => {
    const fetchUserAvatar = async () => {
      if (session?.user?.id) {
        try {
          const response = await fetch('/api/user/profile')
          if (response.ok) {
            const data = await response.json()
            setUserAvatar(data.avatar)
          }
        } catch (error) {
          console.error('Failed to fetch user avatar:', error)
        }
      } else {
        setUserAvatar(null)
      }
    }

    fetchUserAvatar()
  }, [session?.user?.id])

  const handleSignOut = async () => {
    await signOut({ 
      callbackUrl: '/auth/signin',
      redirect: true 
    })
  }

  // 检查当前页面是否为活跃状态
  const isActive = (path: string) => {
    if (!pathname) return false

    if (path === '/products' && pathname.startsWith('/products')) {
      return true
    }
    if (path === '/demands' && pathname.startsWith('/demands')) {
      return true
    }
    if (path === '/profile' && pathname.startsWith('/profile')) {
      return true
    }
    if (path === '/help' && pathname.startsWith('/help')) {
      return true
    }
    if (path === '/about' && pathname === '/about') {
      return true
    }
    return pathname === path
  }

  // 获取链接样式
  const getLinkClass = (path: string) => {
    const baseClass = "text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
    const activeClass = "text-blue-600 font-medium"
    
    return isActive(path) ? `${baseClass} ${activeClass}` : baseClass
  }

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* 左侧：Logo图片 + BitMarket文字 */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
              <img
                src="/logo.jpg"
                alt="BitMarket Logo"
                className="w-10 h-10 rounded-lg object-cover"
              />
              <span className="text-xl font-bold text-gray-900">BitMarket</span>
            </Link>
          </div>

          {/* 右侧：导航项目 */}
          <div className="flex items-center space-x-1">
            {session ? (
              <>
                {/* 浏览商品 */}
                <Link
                  href="/products"
                  className={getLinkClass('/products')}
                >
                  浏览商品
                </Link>

                {/* 需求广场 */}
                <Link
                  href="/demands"
                  className={getLinkClass('/demands')}
                >
                  需求广场
                </Link>

                {/* 帮助中心 */}
                <Link
                  href="/help"
                  className={getLinkClass('/help')}
                >
                  帮助中心
                </Link>

                {/* 关于 */}
                <Link
                  href="/about"
                  className={getLinkClass('/about')}
                >
                  关于
                </Link>

                {/* 用户头像 */}
                <Link
                  href="/profile"
                  className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors group"
                  title="个人中心"
                >
                  {userAvatar ? (
                    <img
                      src={userAvatar}
                      alt="用户头像"
                      className="w-8 h-8 rounded-full object-cover border-2 border-gray-200 group-hover:border-gray-300 transition-colors"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center group-hover:bg-gray-300 transition-colors">
                      <UserIcon className="w-5 h-5 text-gray-500" />
                    </div>
                  )}
                  <span className="hidden sm:inline">个人中心</span>
                </Link>

                {/* 退出登录按钮 - 只在个人中心页面显示 */}
                {pathname === '/profile' && (
                  <button
                    onClick={handleSignOut}
                    className="ml-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    退出登录
                  </button>
                )}
              </>
            ) : (
              <>
                {/* 未登录状态 */}
                <Link
                  href="/products"
                  className={getLinkClass('/products')}
                >
                  浏览商品
                </Link>
                <Link
                  href="/help"
                  className={getLinkClass('/help')}
                >
                  帮助中心
                </Link>
                <Link
                  href="/about"
                  className={getLinkClass('/about')}
                >
                  关于
                </Link>
                <Link
                  href="/auth/signin"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  登录
                </Link>
                <Link
                  href="/auth/register"
                  className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  注册
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}

export { Navbar }
export default Navbar
